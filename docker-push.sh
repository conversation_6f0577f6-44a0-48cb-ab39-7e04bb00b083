#!/bin/bash

# Script untuk push Docker images ke Docker Hub
# Pastikan sudah login: docker login

echo "🚀 Pushing Sumopod Docker Images to Docker Hub..."

# Tag images dengan username Docker Hub
DOCKER_USERNAME="your-dockerhub-username"

echo "📦 Tagging backend image..."
docker tag sumopod-project-backend:latest $DOCKER_USERNAME/sumopod-backend:latest

echo "📦 Tagging frontend image..."
docker tag sumopod-project-frontend:latest $DOCKER_USERNAME/sumopod-frontend:latest

echo "⬆️ Pushing backend image..."
docker push $DOCKER_USERNAME/sumopod-backend:latest

echo "⬆️ Pushing frontend image..."
docker push $DOCKER_USERNAME/sumopod-frontend:latest

echo "✅ Done! Images pushed to Docker Hub"
echo "Backend: $DOCKER_USERNAME/sumopod-backend:latest"
echo "Frontend: $DOCKER_USERNAME/sumopod-frontend:latest"
