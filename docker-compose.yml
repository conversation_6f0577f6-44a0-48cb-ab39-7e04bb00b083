version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: sumopod
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  # Backend
  backend:
    build: ./hono-backend
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    environment:
      - DATABASE_URL=********************************************/sumopod
    env_file:
      - .env

  # Frontend
  frontend:
    build: ./sumopod
    ports:
      - "3000:80"
    depends_on:
      - backend

volumes:
  postgres_data:
